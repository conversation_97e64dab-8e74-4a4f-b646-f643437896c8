{"scenarios": [{"id": "basic-navigation", "name": "Basic Website Navigation", "description": "Test basic navigation functionality on a website", "url": "https://example.com", "steps": [{"action": "navigate", "target": "/"}, {"action": "click", "target": "nav a[href='/about']"}, {"action": "verify", "target": "h1", "expected": "About Us"}]}, {"id": "form-submission", "name": "Form Submission Test", "description": "Test form submission functionality", "url": "https://example.com/contact", "steps": [{"action": "navigate", "target": "/contact"}, {"action": "fill", "target": "input[name='name']", "value": "Test User"}, {"action": "fill", "target": "input[name='email']", "value": "<EMAIL>"}, {"action": "fill", "target": "textarea[name='message']", "value": "This is a test message"}, {"action": "click", "target": "button[type='submit']"}, {"action": "verify", "target": ".success-message", "expected": "Thank you for your message"}]}]}