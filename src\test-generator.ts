import * as fs from 'fs/promises';
import * as path from 'path';
import { MC<PERSON>laywrightClient, TestScenario, TestStep } from './mcp-client.js';

export interface GeneratorConfig {
  outputDir: string;
  templateDir: string;
  defaultTimeout: number;
}

export class PlaywrightTestGenerator {
  constructor(
    private mcpClient: MCPPlaywrightClient,
    private config: GeneratorConfig
  ) {}

  async generateTestFromScenario(scenario: TestScenario): Promise<string> {
    try {
      // Load the test template
      const templatePath = path.join(this.config.templateDir, 'basic-test.template.ts');
      const template = await fs.readFile(templatePath, 'utf-8');

      // Generate test steps code
      const testStepsCode = this.generateTestStepsCode(scenario.steps);
      const assertionsCode = this.generateAssertionsCode(scenario.steps);

      // Replace template placeholders
      let testCode = template
        .replace('{{testSuiteName}}', scenario.name)
        .replace('{{testName}}', scenario.description)
        .replace('{{baseUrl}}', scenario.url)
        .replace('{{testSteps}}', testStepsCode)
        .replace('{{assertions}}', assertionsCode)
        .replace('{{beforeEachCode}}', '// Setup completed')
        .replace('{{afterEachCode}}', '// Cleanup completed');

      return testCode;
    } catch (error) {
      console.error('Failed to generate test from scenario:', error);
      throw error;
    }
  }

  private generateTestStepsCode(steps: TestStep[]): string {
    return steps.map(step => {
      switch (step.action) {
        case 'navigate':
          return `    await page.goto('${step.target}');`;
        
        case 'click':
          return `    await page.locator('${step.target}').click();`;
        
        case 'fill':
          return `    await page.locator('${step.target}').fill('${step.value}');`;
        
        case 'wait':
          const timeout = step.timeout || this.config.defaultTimeout;
          return `    await page.waitForTimeout(${timeout});`;
        
        case 'screenshot':
          return `    await page.screenshot({ path: 'screenshots/${step.target}' });`;
        
        default:
          return `    // Unknown action: ${step.action}`;
      }
    }).join('\n');
  }

  private generateAssertionsCode(steps: TestStep[]): string {
    const verifySteps = steps.filter(step => step.action === 'verify');
    
    if (verifySteps.length === 0) {
      return '    // No additional assertions';
    }

    return verifySteps.map(step => {
      if (step.expected) {
        return `    await expect(page.locator('${step.target}')).toContainText('${step.expected}');`;
      } else {
        return `    await expect(page.locator('${step.target}')).toBeVisible();`;
      }
    }).join('\n');
  }

  async saveGeneratedTest(testCode: string, filename: string): Promise<string> {
    try {
      // Ensure output directory exists
      await fs.mkdir(this.config.outputDir, { recursive: true });

      // Generate full file path
      const filePath = path.join(this.config.outputDir, filename);

      // Write the test file
      await fs.writeFile(filePath, testCode, 'utf-8');

      console.log(`Generated test saved to: ${filePath}`);
      return filePath;
    } catch (error) {
      console.error('Failed to save generated test:', error);
      throw error;
    }
  }

  async generateTestsFromScenariosFile(scenariosPath: string): Promise<string[]> {
    try {
      // Read scenarios file
      const scenariosContent = await fs.readFile(scenariosPath, 'utf-8');
      const scenariosData = JSON.parse(scenariosContent);

      const generatedFiles: string[] = [];

      // Generate tests for each scenario
      for (const scenario of scenariosData.scenarios) {
        const testCode = await this.generateTestFromScenario(scenario);
        const filename = `${scenario.id}.spec.ts`;
        const filePath = await this.saveGeneratedTest(testCode, filename);
        generatedFiles.push(filePath);
      }

      return generatedFiles;
    } catch (error) {
      console.error('Failed to generate tests from scenarios file:', error);
      throw error;
    }
  }

  async exploreAndGenerateTest(url: string, testName: string): Promise<string> {
    try {
      // Use MCP client to explore the website
      const pageInfo = await this.mcpClient.capturePageInfo(url);

      // Create a basic scenario from the exploration
      const scenario: TestScenario = {
        id: testName.toLowerCase().replace(/\s+/g, '-'),
        name: testName,
        description: `Auto-generated test for ${url}`,
        url: url,
        steps: [
          { action: 'navigate', target: '/' },
          { action: 'screenshot', target: `${testName}-initial.png` },
          { action: 'verify', target: 'body', expected: '' }
        ]
      };

      // Generate and save the test
      const testCode = await this.generateTestFromScenario(scenario);
      const filename = `${scenario.id}.spec.ts`;
      return await this.saveGeneratedTest(testCode, filename);
    } catch (error) {
      console.error('Failed to explore and generate test:', error);
      throw error;
    }
  }
}
