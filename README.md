# MCP-Playwright Automated Test Suite

A fully automated test suite that combines the power of Model Context Protocol (MCP) servers with Playwright for intelligent test generation and execution.

## 🚀 Features

- **Automated Test Generation**: Generate Playwright tests from scenarios or by exploring websites
- **MCP Server Integration**: Leverage MCP servers for intelligent test creation
- **Multi-Browser Testing**: Run tests across Chromium, Firefox, and WebKit
- **CI/CD Integration**: Automated testing with GitHub Actions
- **Test Data Management**: Organized fixtures and test data
- **Interactive Mode**: Generate tests interactively from the command line

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd mcp-server-test-2
   ```

2. **Install dependencies**
   ```bash
   npm run setup
   ```

3. **Verify installation**
   ```bash
   npm test
   ```

## 🎯 Quick Start

### Generate Tests from Scenarios

```bash
# Generate tests from predefined scenarios
npm run mcp:generate scenarios

# Run the generated tests
npm test
```

### Explore a Website and Generate Tests

```bash
# Explore a website and generate tests
npm run mcp:generate explore https://example.com "Example Site Test"

# Run in interactive mode
npm run mcp:generate interactive
```

### Run Tests

```bash
# Run all tests
npm test

# Run tests in headed mode (visible browser)
npm run test:headed

# Run tests with UI mode
npm run test:ui

# Debug tests
npm run test:debug
```

## 📁 Project Structure

```
├── .github/workflows/     # GitHub Actions workflows
├── src/                   # Source code
│   ├── mcp-client.ts     # MCP server client
│   ├── test-generator.ts # Test generation utilities
│   └── test-data-manager.ts # Test data management
├── scripts/              # Automation scripts
│   └── generate-tests.js # Main test generation script
├── test-data/            # Test fixtures and scenarios
│   ├── fixtures.json     # Test data fixtures
│   └── scenarios.json    # Test scenarios
├── test-templates/       # Test templates
├── tests/                # Manual tests
├── tests/generated/      # Auto-generated tests
└── playwright.config.ts  # Playwright configuration
```

## 🔧 Configuration

### MCP Configuration (`mcp-config.json`)

Configure MCP servers and test generation settings:

```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-playwright"]
    }
  },
  "testGeneration": {
    "outputDir": "./tests/generated",
    "templateDir": "./test-templates",
    "defaultTimeout": 30000
  }
}
```

### Test Scenarios (`test-data/scenarios.json`)

Define test scenarios for automatic generation:

```json
{
  "scenarios": [
    {
      "id": "basic-navigation",
      "name": "Basic Website Navigation",
      "url": "https://example.com",
      "steps": [
        { "action": "navigate", "target": "/" },
        { "action": "click", "target": "nav a[href='/about']" },
        { "action": "verify", "target": "h1", "expected": "About Us" }
      ]
    }
  ]
}
```

## 🤖 Automation

### GitHub Actions

The project includes automated workflows:

- **Daily Test Generation**: Automatically generates and runs tests daily
- **PR Testing**: Runs tests on pull requests
- **Manual Triggers**: Manually trigger test generation with custom parameters

### Scheduled Testing

Tests run automatically:
- Daily at 2 AM UTC
- On every push to main/master
- On pull requests

## 📊 Test Data Management

### Fixtures

Manage test data in `test-data/fixtures.json`:

```json
{
  "users": {
    "testUser": {
      "name": "Test User",
      "email": "<EMAIL>"
    }
  },
  "selectors": {
    "common": {
      "navigation": "nav",
      "submitButton": "button[type='submit']"
    }
  }
}
```

### Using Test Data

```typescript
import { TestDataManager } from '../src/test-data-manager';

const dataManager = new TestDataManager();
const user = await dataManager.getUser('testUser');
const selector = await dataManager.getSelector('common', 'navigation');
```

## 🎮 Interactive Mode

Start interactive mode for real-time test generation:

```bash
npm run mcp:generate interactive
```

Available commands:
- `explore <url> [testName]` - Explore a website and generate tests
- `scenarios` - Generate tests from scenarios file
- `list` - List generated tests
- `clean` - Clean generated tests directory
- `help` - Show available commands
- `exit` - Exit interactive mode

## 🧪 Example Generated Test

```typescript
import { test, expect } from '@playwright/test';

test.describe('Basic Website Navigation', () => {
  test('Basic navigation functionality', async ({ page }) => {
    await page.goto('https://example.com');
    await page.locator('nav a[href="/about"]').click();
    await expect(page.locator('h1')).toContainText('About Us');
  });
});
```

## 🔍 Debugging

### Debug Failed Tests

```bash
# Run tests in debug mode
npm run test:debug

# View test report
npm run test:report
```

### Troubleshooting

1. **MCP Server Connection Issues**
   - Ensure MCP server is properly installed
   - Check network connectivity
   - Verify configuration in `mcp-config.json`

2. **Test Generation Failures**
   - Check scenario syntax in `scenarios.json`
   - Verify target URLs are accessible
   - Review error logs in console

## 📈 Best Practices

1. **Test Organization**
   - Keep scenarios focused and atomic
   - Use descriptive test names
   - Organize tests by feature/functionality

2. **Data Management**
   - Use fixtures for consistent test data
   - Avoid hardcoded values in tests
   - Implement data cleanup strategies

3. **CI/CD Integration**
   - Run tests on multiple browsers
   - Use appropriate timeouts
   - Archive test artifacts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review existing GitHub issues
3. Create a new issue with detailed information

---

**Happy Testing! 🎭**
