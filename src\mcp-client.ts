import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

export interface MCPConfig {
  command: string;
  args: string[];
  env?: Record<string, string>;
}

export interface TestScenario {
  id: string;
  name: string;
  description: string;
  url: string;
  steps: TestStep[];
}

export interface TestStep {
  action: 'navigate' | 'click' | 'fill' | 'verify' | 'wait' | 'screenshot';
  target: string;
  value?: string;
  expected?: string;
  timeout?: number;
}

export class MCPPlaywrightClient {
  private client: Client | null = null;
  private transport: StdioClientTransport | null = null;

  constructor(private config: MCPConfig) {}

  async connect(): Promise<void> {
    try {
      // Spawn the MCP server process
      const serverProcess = spawn(this.config.command, this.config.args, {
        env: { ...process.env, ...this.config.env },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Create transport and client
      this.transport = new StdioClientTransport({
        reader: serverProcess.stdout,
        writer: serverProcess.stdin
      });

      this.client = new Client({
        name: 'playwright-test-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      });

      await this.client.connect(this.transport);
      console.log('Connected to MCP server');
    } catch (error) {
      console.error('Failed to connect to MCP server:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
    }
    if (this.transport) {
      await this.transport.close();
      this.transport = null;
    }
  }

  async exploreWebsite(url: string): Promise<any> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.callTool({
        name: 'playwright_navigate',
        arguments: { url }
      });

      return result;
    } catch (error) {
      console.error('Failed to explore website:', error);
      throw error;
    }
  }

  async generateTestFromScenario(scenario: TestScenario): Promise<string> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      // Use MCP to analyze the scenario and generate test code
      const result = await this.client.callTool({
        name: 'generate_playwright_test',
        arguments: {
          scenario: scenario,
          template: 'basic-test'
        }
      });

      return result.content as string;
    } catch (error) {
      console.error('Failed to generate test:', error);
      throw error;
    }
  }

  async capturePageInfo(url: string): Promise<any> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.callTool({
        name: 'playwright_capture_page_info',
        arguments: { url }
      });

      return result;
    } catch (error) {
      console.error('Failed to capture page info:', error);
      throw error;
    }
  }
}
